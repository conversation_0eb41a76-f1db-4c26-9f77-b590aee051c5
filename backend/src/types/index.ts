// Backend types for MiCA Therapy Simulation

export interface AgentConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  persona?: string;
}

// Therapist Persona Strategy Types
export type TherapistPersonaType = 'cbt-only' | 'mi-fixed-pretreatment' | 'dynamic-adaptive';

export interface TherapistPersonaConfig {
  id: TherapistPersonaType;
  name: string;
  description: string;
  strategy: TherapeuticStrategy;
}

export interface TherapeuticStrategy {
  type: TherapistPersonaType;
  readinessThreshold?: number; // For switching strategies
  allowSwitching: boolean;
  initialApproach: 'CBT' | 'MI';
  switchingRules?: {
    cbtThreshold: number;
    miThreshold: number;
    allowReverseSwitch: boolean;
  };
}

// Therapeutic Framework Types
export interface TherapeuticApproachInfo {
  id: 'motivational-interviewing' | 'cognitive-behavioral-therapy';
  name: string;
  selectedTechnique: {
    id: string;
    name: string;
    description: string;
  };
}

export interface ReadinessScore {
  score: number; // 1-10 scale
  factors: {
    sentiment: {
      value: 'positive' | 'negative' | 'neutral';
      weight: number;
      contribution: number;
    };
    sentimentIntensity: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    motivation: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    motivationType: {
      value: 'intrinsic' | 'extrinsic' | 'mixed';
      weight: number;
      contribution: number;
    };
    engagement: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    engagementPatterns: {
      value: string[];
      weight: number;
      contribution: number;
    };
  };
  reasoning: string;
  recommendedApproach: 'CBT' | 'MI';
  indicators: {
    positive: string[];
    negative: string[];
  };
}

export interface PatientAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral';
  sentimentIntensity: 'low' | 'medium' | 'high';
  motivationLevel: 'low' | 'medium' | 'high';
  motivationType: 'intrinsic' | 'extrinsic' | 'mixed';
  engagementLevel: 'low' | 'medium' | 'high';
  engagementPatterns: string[];
  readinessScore: ReadinessScore;
}

export interface TherapistAgent extends AgentConfig {
  type: 'therapist';
  analysisPrompts: {
    sentiment: string;
    motivation: string;
    engagement: string;
  };
}

// Multi-Therapist Response Types
export interface MultiTherapistResponse {
  cbtOnly: TherapistResponse;
  miFixedPretreatment: TherapistResponse;
  dynamicAdaptive: TherapistResponse;
  patientMessage: string;
  timestamp: string;
}

export interface TherapistPersonaResponse extends TherapistResponse {
  personaType: TherapistPersonaType;
  personaName: string;
  strategyState?: {
    currentApproach: 'CBT' | 'MI';
    hasSwitch: boolean;
    switchReason?: string;
    sessionPhase: 'opening' | 'middle' | 'closing';
  };
}

export interface PatientAgent extends AgentConfig {
  type: 'patient';
  emotionalState: EmotionalState;
  backstory?: string;
  currentConcerns?: string[];
}

export interface EmotionalState {
  mood: 'depressed' | 'anxious' | 'neutral' | 'hopeful' | 'frustrated';
  energy: 'low' | 'medium' | 'high';
  openness: 'closed' | 'guarded' | 'open' | 'very_open';
  trust: number; // 0-100
}

export interface ConversationContext {
  id: string;
  messages: Array<{
    id: string;
    conversationId: string;
    sender: 'therapist' | 'patient';
    content: string;
    thinking: string;
    metadata: {
      confidence: number;
      processingTime: number;
      patientAnalysis?: PatientAnalysis;
      therapeuticApproach?: TherapeuticApproachInfo;
    };
    timestamp: string;
  }>;
  currentTurn: number;
  maxTurns: number;
  status: 'active' | 'completed' | 'paused';
}

// Multi-Therapist Conversation Context
export interface MultiTherapistConversationContext {
  id: string;
  patientMessages: Array<{
    id: string;
    conversationId: string;
    content: string;
    timestamp: string;
  }>;
  therapistConversations: {
    cbtOnly: ConversationContext;
    miFixedPretreatment: ConversationContext;
    dynamicAdaptive: ConversationContext;
  };
  currentTurn: number;
  maxTurns: number;
  status: 'active' | 'completed' | 'paused';
  studyMetadata: {
    startTime: string;
    patientPersonaId: string;
    studyConfiguration: {
      readinessThresholds: {
        cbtMinimum: number;
        miMaximum: number;
      };
      allowDynamicSwitching: boolean;
    };
  };
}

export interface AgentResponse {
  message: string;
  thinking: string;
  metadata: {
    confidence: number;
    processingTime: number;
  };
}

export interface TherapistResponse extends AgentResponse {
  metadata: AgentResponse['metadata'] & {
    patientAnalysis?: PatientAnalysis;
    therapeuticApproach?: TherapeuticApproachInfo;
  };
}

export interface PatientResponse extends AgentResponse {
  // Patient responses don't include self-analysis
}

export interface OpenAIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature: number;
  max_completion_tokens: number;
}

export interface DatabaseConversation {
  id: string;
  status: string;
  config: any;
  created_at: string;
  updated_at: string;
}

export interface DatabaseMessage {
  id: string;
  conversation_id: string;
  sender: string;
  content: string;
  metadata: any;
  created_at: string;
}

export interface DatabaseThought {
  id: string;
  conversation_id: string;
  agent_type: string;
  content: string;
  message_id?: string;
  type: string;
  created_at: string;
}

export interface WebSocketClient {
  id: string;
  socket: any;
  conversationId?: string;
  lastActivity: string;
}

export interface ConversationOrchestrator {
  conversationId: string;
  therapist: TherapistAgent;
  patient: PatientAgent;
  context: ConversationContext;
  clients: WebSocketClient[];
}

// CBT Evaluation Types
export interface CBTEvaluationDimension {
  name: string;
  score: number; // 0, 2, 4, or 6
  criteria: string;
  rationale?: string;
}

export interface CBTEvaluationResult {
  id: string;
  conversationId: string;
  evaluationTimestamp: string;
  dimensions: {
    cbtValidity: CBTEvaluationDimension;
    cbtAppropriateness: CBTEvaluationDimension;
    cbtAccuracy: CBTEvaluationDimension;
    esAppropriateness: CBTEvaluationDimension;
    stability: CBTEvaluationDimension;
  };
  overallScore: number; // Average of all dimension scores
  overallAssessment: 'poor' | 'fair' | 'good' | 'excellent';
  conversationSummary: string;
  recommendations: string[];
  metadata: {
    totalMessages: number;
    sessionDuration: number; // in minutes
    evaluationModel: string;
    evaluationVersion: string;
  };
}

// Comparative Evaluation Types
export interface ComparativeCBTEvaluationResult {
  id: string;
  conversationId: string;
  evaluationTimestamp: string;
  evaluations: {
    cbtOnly: CBTEvaluationResult;
    miFixedPretreatment: CBTEvaluationResult;
    dynamicAdaptive: CBTEvaluationResult;
  };
  comparison: {
    bestPerforming: TherapistPersonaType;
    scoreComparison: {
      cbtOnly: number;
      miFixedPretreatment: number;
      dynamicAdaptive: number;
    };
    dimensionComparison: {
      [dimension: string]: {
        cbtOnly: number;
        miFixedPretreatment: number;
        dynamicAdaptive: number;
        winner: TherapistPersonaType;
      };
    };
    insights: string[];
    recommendations: string[];
  };
  studyMetadata: {
    patientPersonaId: string;
    sessionDuration: number;
    totalPatientMessages: number;
    readinessScoreProgression: {
      initial: number;
      final: number;
      average: number;
    };
  };
}

export interface CBTEvaluationRequest {
  conversationId: string;
  messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
  }>;
  sessionMetadata?: {
    duration?: number;
    patientPersona?: string;
    therapeuticApproaches?: string[];
  };
}

export interface DatabaseEvaluation {
  id: string;
  conversation_id: string;
  evaluation_data: CBTEvaluationResult;
  created_at: string;
  updated_at: string;
}
