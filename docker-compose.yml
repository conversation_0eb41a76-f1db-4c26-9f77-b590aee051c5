version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000
      - VITE_WS_URL=ws://localhost:3000
    depends_on:
      - backend
    networks:
      - mica-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
    env_file:
      - .env
    networks:
      - mica-network
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: Local PostgreSQL for development (if not using Supabase cloud)
  # postgres:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: mica_dev
  #     POSTGRES_USER: mica_user
  #     POSTGRES_PASSWORD: mica_password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./database/migrations:/docker-entrypoint-initdb.d
  #   networks:
  #     - mica-network

  # Optional: Redis for caching and session management
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   networks:
  #     - mica-network

networks:
  mica-network:
    driver: bridge

# volumes:
#   postgres_data:
