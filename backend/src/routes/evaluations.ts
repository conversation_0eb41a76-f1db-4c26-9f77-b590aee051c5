// CBT Evaluation Routes for MiCA Therapy Simulation
import express from 'express';
import { CBTEvaluationObserverService } from '../services/agents/cbt-evaluation-observer.js';
import { CBTEvaluationRequest, CBTEvaluationResult, DatabaseEvaluation } from '../types/index.js';
import { supabase } from '../config/database.js';

const router = express.Router();
const evaluationService = new CBTEvaluationObserverService();

/**
 * POST /api/evaluations/evaluate
 * Trigger CBT evaluation for a completed conversation
 */
router.post('/evaluate', async (req, res) => {
  try {
    const evaluationRequest: CBTEvaluationRequest = req.body;

    // Validate request
    if (!evaluationRequest.conversationId || !evaluationRequest.messages || evaluationRequest.messages.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid evaluation request',
        message: 'conversationId and messages are required'
      });
    }

    console.log(`🔍 Starting evaluation for conversation ${evaluationRequest.conversationId}`);

    // Check if evaluation already exists
    const { data: existingEvaluation, error: checkError } = await supabase
      .from('cbt_evaluations')
      .select('id')
      .eq('conversation_id', evaluationRequest.conversationId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing evaluation:', checkError);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to check existing evaluation'
      });
    }

    if (existingEvaluation) {
      return res.status(409).json({
        success: false,
        error: 'Evaluation already exists',
        message: `Evaluation already exists for conversation ${evaluationRequest.conversationId}`
      });
    }

    // Perform evaluation
    const evaluationResult = await evaluationService.evaluateSession(evaluationRequest);

    // Store evaluation in database
    const { data: savedEvaluation, error: saveError } = await supabase
      .from('cbt_evaluations')
      .insert({
        conversation_id: evaluationRequest.conversationId,
        evaluation_data: evaluationResult,
        overall_score: evaluationResult.overallScore,
        overall_assessment: evaluationResult.overallAssessment
      })
      .select()
      .single();

    if (saveError) {
      console.error('Error saving evaluation:', saveError);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to save evaluation result'
      });
    }

    // Store individual dimension scores
    const dimensionInserts = Object.entries(evaluationResult.dimensions).map(([dimensionName, dimension]) => ({
      evaluation_id: savedEvaluation.id,
      dimension_name: dimensionName,
      score: dimension.score,
      criteria: dimension.criteria,
      rationale: dimension.rationale
    }));

    const { error: dimensionError } = await supabase
      .from('evaluation_dimensions')
      .insert(dimensionInserts);

    if (dimensionError) {
      console.error('Error saving dimension scores:', dimensionError);
      // Continue despite dimension save error - main evaluation is saved
    }

    console.log(`✅ Evaluation completed and saved for conversation ${evaluationRequest.conversationId}`);

    res.json({
      success: true,
      data: evaluationResult,
      message: 'Evaluation completed successfully'
    });

  } catch (error) {
    console.error('❌ Error during evaluation:', error);
    res.status(500).json({
      success: false,
      error: 'Evaluation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/evaluations/:conversationId
 * Get evaluation result for a specific conversation
 */
router.get('/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const { data: evaluation, error } = await supabase
      .from('cbt_evaluations')
      .select('*')
      .eq('conversation_id', conversationId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return res.status(404).json({
          success: false,
          error: 'Evaluation not found',
          message: `No evaluation found for conversation ${conversationId}`
        });
      }

      console.error('Error fetching evaluation:', error);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to fetch evaluation'
      });
    }

    res.json({
      success: true,
      data: evaluation.evaluation_data,
      metadata: {
        id: evaluation.id,
        createdAt: evaluation.created_at,
        updatedAt: evaluation.updated_at
      }
    });

  } catch (error) {
    console.error('❌ Error fetching evaluation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch evaluation',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/evaluations
 * Get all evaluations with optional filtering
 */
router.get('/', async (req, res) => {
  try {
    const { 
      limit = 50, 
      offset = 0, 
      assessment,
      minScore,
      maxScore 
    } = req.query;

    let query = supabase
      .from('cbt_evaluations')
      .select('id, conversation_id, overall_score, overall_assessment, created_at, evaluation_data')
      .order('created_at', { ascending: false })
      .range(Number(offset), Number(offset) + Number(limit) - 1);

    // Apply filters
    if (assessment) {
      query = query.eq('overall_assessment', assessment);
    }

    if (minScore) {
      query = query.gte('overall_score', Number(minScore));
    }

    if (maxScore) {
      query = query.lte('overall_score', Number(maxScore));
    }

    const { data: evaluations, error } = await query;

    if (error) {
      console.error('Error fetching evaluations:', error);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to fetch evaluations'
      });
    }

    res.json({
      success: true,
      data: evaluations,
      metadata: {
        count: evaluations.length,
        limit: Number(limit),
        offset: Number(offset)
      }
    });

  } catch (error) {
    console.error('❌ Error fetching evaluations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch evaluations',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * DELETE /api/evaluations/:conversationId
 * Delete evaluation for a specific conversation
 */
router.delete('/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const { error } = await supabase
      .from('cbt_evaluations')
      .delete()
      .eq('conversation_id', conversationId);

    if (error) {
      console.error('Error deleting evaluation:', error);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to delete evaluation'
      });
    }

    res.json({
      success: true,
      message: `Evaluation deleted for conversation ${conversationId}`
    });

  } catch (error) {
    console.error('❌ Error deleting evaluation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete evaluation',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/evaluations/stats/summary
 * Get evaluation statistics summary
 */
router.get('/stats/summary', async (req, res) => {
  try {
    const { data: stats, error } = await supabase
      .from('cbt_evaluations')
      .select('overall_score, overall_assessment');

    if (error) {
      console.error('Error fetching evaluation stats:', error);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to fetch evaluation statistics'
      });
    }

    const summary = {
      totalEvaluations: stats.length,
      averageScore: stats.length > 0 ? stats.reduce((sum, evaluation) => sum + evaluation.overall_score, 0) / stats.length : 0,
      assessmentDistribution: {
        excellent: stats.filter(s => s.overall_assessment === 'excellent').length,
        good: stats.filter(s => s.overall_assessment === 'good').length,
        fair: stats.filter(s => s.overall_assessment === 'fair').length,
        poor: stats.filter(s => s.overall_assessment === 'poor').length
      }
    };

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('❌ Error fetching evaluation stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch evaluation statistics',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

export default router;
