// Test script to verify differentiated patient responses
import { OpenAIService } from './src/services/openai';
import { PatientAgentService } from './src/services/agents/patient';
import { PersonaService } from './src/services/persona-service';

async function testDifferentiatedResponses() {
  console.log('🧪 Testing differentiated patient responses...\n');

  try {
    // Initialize services
    const openaiService = new OpenAIService();
    
    // Get Emma persona
    const emmaPersona = PersonaService.getPersonaById('emma-anxiety');
    if (!emmaPersona) {
      throw new Error('Emma persona not found');
    }
    
    const patientConfig = PersonaService.convertToPatientConfig(emmaPersona);
    const patientAgent = new PatientAgentService(openaiService, patientConfig);

    // Mock therapist messages with different approaches
    const therapistMessages = {
      cbtOnly: {
        content: "I notice you mentioned feeling trapped. Can you help me understand what specific thoughts go through your mind when you feel this way? What evidence do you have that supports or challenges these thoughts?",
        therapeuticApproach: "Cognitive Behavioral Therapy",
        technique: "Thought Challenging"
      },
      miFixedPretreatment: {
        content: "It sounds like you're experiencing some really difficult feelings about being trapped. I'm wondering what it's like for you to share this with me right now?",
        therapeuticApproach: "Motivational Interviewing", 
        technique: "Reflective Listening"
      },
      dynamicAdaptive: {
        content: "You've mentioned feeling trapped, and I can hear how difficult that is for you. What would it mean to you if things could be different?",
        therapeuticApproach: "Dynamic Adaptive",
        technique: "Open-Ended Questions"
      }
    };

    // Mock conversation contexts (simplified)
    const conversationContexts = {
      cbtOnly: {
        id: 'test-cbt',
        messages: [],
        currentTurn: 1,
        maxTurns: 10,
        status: 'active'
      },
      miFixedPretreatment: {
        id: 'test-mi',
        messages: [],
        currentTurn: 1,
        maxTurns: 10,
        status: 'active'
      },
      dynamicAdaptive: {
        id: 'test-dynamic',
        messages: [],
        currentTurn: 1,
        maxTurns: 10,
        status: 'active'
      }
    };

    console.log('📝 Generating differentiated responses...\n');

    // Generate differentiated responses
    const responses = await patientAgent.generateMultiTherapistResponse(
      therapistMessages,
      conversationContexts
    );

    // Display results
    console.log('🎭 PATIENT RESPONSES TO DIFFERENT THERAPEUTIC APPROACHES:\n');
    
    console.log('🧠 CBT Response (Thought Challenging):');
    console.log('Message:', responses.cbtOnly.message);
    console.log('Thinking:', responses.cbtOnly.thinking);
    console.log('Confidence:', responses.cbtOnly.metadata.confidence);
    console.log('');

    console.log('🤝 MI Response (Reflective Listening):');
    console.log('Message:', responses.miFixedPretreatment.message);
    console.log('Thinking:', responses.miFixedPretreatment.thinking);
    console.log('Confidence:', responses.miFixedPretreatment.metadata.confidence);
    console.log('');

    console.log('🔄 Dynamic Adaptive Response (Open-Ended Questions):');
    console.log('Message:', responses.dynamicAdaptive.message);
    console.log('Thinking:', responses.dynamicAdaptive.thinking);
    console.log('Confidence:', responses.dynamicAdaptive.metadata.confidence);
    console.log('');

    // Analyze differences
    console.log('📊 ANALYSIS:');
    console.log('- CBT response length:', responses.cbtOnly.message.length, 'characters');
    console.log('- MI response length:', responses.miFixedPretreatment.message.length, 'characters');
    console.log('- Dynamic response length:', responses.dynamicAdaptive.message.length, 'characters');
    
    const cbtWords = responses.cbtOnly.message.toLowerCase();
    const miWords = responses.miFixedPretreatment.message.toLowerCase();
    const dynamicWords = responses.dynamicAdaptive.message.toLowerCase();
    
    console.log('- CBT contains analytical words:', /think|thought|evidence|specific|analyze/.test(cbtWords));
    console.log('- MI contains exploratory words:', /feel|maybe|part of me|not sure|wonder/.test(miWords));
    console.log('- Dynamic adapts to approach:', dynamicWords.length > 0);

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testDifferentiatedResponses();
