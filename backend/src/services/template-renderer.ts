// Template Rendering Service for MiCA Therapy Simulation
// Handles reading and rendering the PATIENT-Psi.md template with persona data

import fs from 'fs/promises';
import path from 'path';
import { DetailedPatientPersona } from '../data/patient-personas.js';

export class TemplateRenderer {
  private static templateCache: Map<string, string> = new Map();

  /**
   * Read and cache the patient prompt template
   */
  private static async getTemplate(templatePath: string): Promise<string> {
    if (this.templateCache.has(templatePath)) {
      return this.templateCache.get(templatePath)!;
    }

    try {
      const template = await fs.readFile(templatePath, 'utf-8');
      this.templateCache.set(templatePath, template);
      return template;
    } catch (error) {
      console.error(`❌ Error reading template from ${templatePath}:`, error);
      throw new Error(`Failed to read template: ${templatePath}`);
    }
  }

  /**
   * Render the PATIENT-Psi.md template with persona data
   */
  static async renderPatientPrompt(persona: DetailedPatientPersona): Promise<string> {
    // Use absolute path from project root
    const templatePath = path.resolve(process.cwd(), '../docs/prompts/PATIENT-Psi.md');
    const template = await this.getTemplate(templatePath);

    // Prepare template variables
    const variables = this.prepareTemplateVariables(persona);

    // Replace all template variables
    let renderedTemplate = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      renderedTemplate = renderedTemplate.replace(regex, value);
    }

    // Handle conditional sections (like intermediateBeliefsDepression)
    renderedTemplate = this.handleConditionalSections(renderedTemplate, variables);

    return renderedTemplate;
  }

  /**
   * Prepare all template variables from persona data
   */
  private static prepareTemplateVariables(persona: DetailedPatientPersona): Record<string, string> {
    const variables: Record<string, string> = {
      // Basic persona info
      name: persona.name,
      age: persona.age.toString(),
      background: persona.background,
      sessionContext: persona.sessionContext,

      // History and situation
      relevantHistory: persona.relevantHistory,
      currentSituation: persona.currentSituation,

      // Cognitive conceptualization
      coreBeliefs: persona.cognitiveConceptualization.coreBeliefs.join(', '),
      intermediateBeliefs: persona.cognitiveConceptualization.intermediateBeliefs.join(', '),
      copingStrategies: persona.cognitiveConceptualization.copingStrategies.join(', '),

      // Thoughts and emotions
      automaticThoughts: persona.automaticThoughts.join(', '),
      primaryEmotions: persona.emotions.primary.join(', '),
      secondaryEmotions: persona.emotions.secondary?.join(', ') || '',

      // Behaviors
      maladaptiveBehaviors: persona.behaviors.maladaptive.join(', '),
      copingMechanisms: persona.behaviors.copingMechanisms.join(', '),
      behavioralPatterns: persona.behaviors.behavioralPatterns.join(', '),

      // Style and concerns
      conversationalStyle: persona.conversationalStyle,
      presentingConcerns: persona.presentingConcerns.join(', '),

      // Emotion intensity levels
      emotionIntensityLevels: this.formatEmotionIntensityLevels(persona.emotions.intensityLevels)
    };

    // Handle optional intermediate beliefs for depression
    if (persona.cognitiveConceptualization.intermediateBeliefsDepression) {
      variables.intermediateBeliefsDepression = persona.cognitiveConceptualization.intermediateBeliefsDepression.join(', ');
    }

    return variables;
  }

  /**
   * Format emotion intensity levels for template
   */
  private static formatEmotionIntensityLevels(intensityLevels?: { [emotion: string]: 'low' | 'medium' | 'high' }): string {
    if (!intensityLevels) return 'moderate intensity across emotions';

    const formatted = Object.entries(intensityLevels)
      .map(([emotion, intensity]) => `${emotion}: ${intensity}`)
      .join(', ');

    return formatted || 'moderate intensity across emotions';
  }

  /**
   * Handle conditional sections in the template
   */
  private static handleConditionalSections(template: string, variables: Record<string, string>): string {
    // Handle {{#intermediateBeliefsDepression}} conditional section
    const conditionalRegex = /{{#intermediateBeliefsDepression}}(.*?){{\/intermediateBeliefsDepression}}/gs;
    
    return template.replace(conditionalRegex, (match, content) => {
      if (variables.intermediateBeliefsDepression) {
        // Replace the variable within the conditional content
        return content.replace(/{{intermediateBeliefsDepression}}/g, variables.intermediateBeliefsDepression);
      }
      return ''; // Remove the entire conditional section if variable doesn't exist
    });
  }

  /**
   * Generic template rendering method for simple variable substitution
   * Supports {{variableName}} syntax
   */
  render(template: string, variables: Record<string, string | number>): string {
    let renderedTemplate = template;

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      renderedTemplate = renderedTemplate.replace(regex, String(value));
    }

    return renderedTemplate;
  }

  /**
   * Static version of the render method
   */
  static render(template: string, variables: Record<string, string | number>): string {
    const renderer = new TemplateRenderer();
    return renderer.render(template, variables);
  }

  /**
   * Clear template cache (useful for development/testing)
   */
  static clearCache(): void {
    this.templateCache.clear();
  }
}
